import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  Plus,
  Minus,
  ChevronRight,
  Loader2,
  Package2,
  PackageIcon,
} from "lucide-react";
import { Input } from "@/components/ui/input";
import React, { useState, useEffect } from "react";
import { sanitizeString } from "@/services/helper";
import { cn } from "@/lib/utils";
import { mcpApi } from "@/app/api/mcp";
import { useQuery } from "@tanstack/react-query";
import Image from "next/image";
import {
  MCPInDB,
  PaginationMetadata,
  PaginatedMCPResponse,
} from "@/shared/interfaces";
import { MCPCategory } from "@/shared/enums";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

interface Tool extends MCPInDB {
  isAdded: boolean;
}

const PAGE_SIZE = 5;

export const EmployeeToolsTable = ({
  setTools,
  initialToolIds = [],
}: {
  setTools: (tools: string[]) => void;
  initialToolIds?: string[];
}) => {
  const [activeCategory, setActiveCategory] = useState<string>("All");
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [addedToolIds, setAddedToolIds] = useState<Set<string>>(
    new Set(initialToolIds)
  );
  const [currentPage, setCurrentPage] = useState<number>(1);

  const {
    data: paginatedMcpResponse,
    isLoading,
    error,
  } = useQuery<PaginatedMCPResponse, Error>({
    queryKey: ["mcps", currentPage, PAGE_SIZE],
    queryFn: () => mcpApi.getMcpServersByUser(currentPage, PAGE_SIZE),
    placeholderData: (previousData: PaginatedMCPResponse | undefined) =>
      previousData,
  });

  useEffect(() => {
    setTools(Array.from(addedToolIds));
  }, [addedToolIds, setTools]);

  const handleAddTool = (toolId: string) => {
    setAddedToolIds((prevIds) => {
      const newIds = new Set(prevIds).add(toolId);
      return newIds;
    });
  };

  const handleRemoveTool = (toolId: string) => {
    setAddedToolIds((prevIds) => {
      const newIds = new Set(prevIds);
      newIds.delete(toolId);
      return newIds;
    });
  };

  const handleCategoryClick = (categoryKey: string) => {
    setActiveCategory(categoryKey);
    setCurrentPage(1);
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
    setCurrentPage(1);
  };

  const mcpArrayFromApi = paginatedMcpResponse?.data?.data || [];
  const apiMetadata: PaginationMetadata | undefined =
    paginatedMcpResponse?.data?.metadata;

  const tools: Tool[] = React.useMemo(() => {
    return (mcpArrayFromApi || []).map((mcp: MCPInDB) => ({
      ...mcp,
      isAdded: addedToolIds.has(mcp.id),
    }));
  }, [mcpArrayFromApi, addedToolIds]);

  const filteredTools = tools.filter((tool) => {
    const toolDepartment =
      typeof tool.department === "string" ? tool.department : "";
    const categoryMatch =
      activeCategory === "All" || toolDepartment === activeCategory;

    const toolName = typeof tool.name === "string" ? tool.name : "";
    const currentSearchTerm = typeof searchTerm === "string" ? searchTerm : "";
    const searchMatch = toolName
      .toLowerCase()
      .includes(currentSearchTerm.toLowerCase());

    return categoryMatch && searchMatch;
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-brand-primary" />
      </div>
    );
  }

  if (error || (!isLoading && !paginatedMcpResponse)) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-brand-secondary-font">
        <Package2 className="h-12 w-12 mb-4" />
        <p>Error fetching tools. Please try again later.</p>
      </div>
    );
  }

  if (!isLoading && paginatedMcpResponse && !paginatedMcpResponse.data) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-brand-secondary-font">
        <Package2 className="h-12 w-12 mb-4" />
        <p>No tool data received. Please try again.</p>
      </div>
    );
  }

  if (!isLoading && apiMetadata && apiMetadata.total === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-brand-secondary-font">
        <Package2 className="h-12 w-12 mb-4" />
        <p>No tools available at the moment.</p>
      </div>
    );
  }

  const truncateDescription = (description: string) => {
    return description.length > 50
      ? `${description.slice(0, 50)}...`
      : description;
  };

  return (
    <div className="flex flex-col gap-4">
      <div className="flex justify-between items-center">
        <div></div>
        <Input
          placeholder="Search tools by name..."
          className="w-full max-w-xs sm:max-w-sm md:max-w-md"
          value={searchTerm}
          onChange={handleSearchChange}
        />
      </div>
      <CategoryRow
        activeCategory={activeCategory}
        onCategoryClick={handleCategoryClick}
      />
      <div className="rounded-lg border border-brand-stroke bg-brand-card shadow-sm">
        <Table>
          <TableHeader>
            <TableRow className="border-b-brand-stroke">
              <TableHead className="w-[200px] px-6 py-4 text-brand-primary-font font-semibold">
                Tools
              </TableHead>
              <TableHead className="px-6 py-4 text-brand-primary-font font-semibold">
                Description
              </TableHead>
              <TableHead className="px-6 py-4 text-brand-primary-font font-semibold">
                Category
              </TableHead>
              <TableHead className="text-right px-6 py-4 text-brand-primary-font font-semibold">
                Add tool
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredTools.length === 0 && !isLoading ? (
              <TableRow>
                <TableCell
                  colSpan={4}
                  className="text-center h-24 text-brand-secondary-font"
                >
                  No tools match your current filters.
                </TableCell>
              </TableRow>
            ) : (
              filteredTools.map((tool) => {
                const isToolAdded = addedToolIds.has(tool.id);
                return (
                  <TableRow
                    key={tool.id}
                    className={`border-b-brand-stroke ${
                      isToolAdded ? "bg-brand-card-hover" : ""
                    }`}
                  >
                    <TableCell className="px-6 py-4">
                      <div className="flex items-center space-x-3">
                        {tool.logo ? (
                          <Image
                            src={tool.logo}
                            alt={tool.name}
                            width={20}
                            height={20}
                          />
                        ) : (
                          <PackageIcon className="w-4 h-4" />
                        )}
                        <span className="font-medium text-brand-primary-font">
                          {tool.name}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell className="px-6 py-4 text-brand-secondary-font">
                      {truncateDescription(tool.description)}
                    </TableCell>
                    <TableCell className="px-6 py-4">
                      <Badge
                        variant="outline"
                        className="border-brand-stroke bg-brand-background text-brand-secondary-font"
                      >
                        {sanitizeString(tool.department)}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right px-6 py-4">
                      {isToolAdded ? (
                        <Button
                          variant="outline"
                          size="sm"
                          className="bg-brand-primary text-brand-white-text hover:bg-brand-primary/90 border-brand-primary"
                          onClick={() => handleRemoveTool(tool.id)}
                        >
                          <Minus className="mr-2 h-4 w-4" />
                          Remove
                        </Button>
                      ) : (
                        <Button
                          variant="outline"
                          size="sm"
                          className="border-brand-primary text-brand-primary hover:bg-brand-clicked"
                          onClick={() => handleAddTool(tool.id)}
                        >
                          <Plus className="mr-2 h-4 w-4" />
                          Add
                        </Button>
                      )}
                    </TableCell>
                  </TableRow>
                );
              })
            )}
          </TableBody>
        </Table>
      </div>

      {apiMetadata && apiMetadata.totalPages > 1 && (
        <Pagination>
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious
                onClick={(e) => {
                  e.preventDefault();
                  if (apiMetadata.hasPreviousPage && currentPage > 1) {
                    setCurrentPage(currentPage - 1);
                  }
                }}
                className={
                  !apiMetadata.hasPreviousPage || currentPage <= 1
                    ? "pointer-events-none opacity-50"
                    : ""
                }
                href="#"
              />
            </PaginationItem>
            {[...Array(apiMetadata.totalPages)].map((_, index) => {
              const pageNum = index + 1;
              const showPage =
                apiMetadata.totalPages <= 7 ||
                pageNum === 1 ||
                pageNum === apiMetadata.totalPages ||
                (pageNum >= currentPage - 1 && pageNum <= currentPage + 1);

              if (showPage) {
                return (
                  <PaginationItem key={pageNum}>
                    <PaginationLink
                      onClick={(e) => {
                        e.preventDefault();
                        setCurrentPage(pageNum);
                      }}
                      isActive={currentPage === pageNum}
                      href="#"
                    >
                      {pageNum}
                    </PaginationLink>
                  </PaginationItem>
                );
              } else if (
                (pageNum === 2 &&
                  currentPage > 3 &&
                  apiMetadata.totalPages > 7) ||
                (pageNum === apiMetadata.totalPages - 1 &&
                  currentPage < apiMetadata.totalPages - 2 &&
                  apiMetadata.totalPages > 7)
              ) {
                return (
                  <PaginationItem key={`ellipsis-${pageNum}`}>
                    <span className="px-2">...</span>
                  </PaginationItem>
                );
              }
              return null;
            })}
            <PaginationItem>
              <PaginationNext
                onClick={(e) => {
                  e.preventDefault();
                  if (
                    apiMetadata.hasNextPage &&
                    currentPage < apiMetadata.totalPages
                  ) {
                    setCurrentPage(currentPage + 1);
                  }
                }}
                className={
                  !apiMetadata.hasNextPage ||
                  currentPage >= apiMetadata.totalPages
                    ? "pointer-events-none opacity-50"
                    : ""
                }
                href="#"
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      )}
    </div>
  );
};

interface CategoryRowProps {
  activeCategory: string;
  onCategoryClick: (categoryKey: string) => void;
}

const CategoryRow = ({ activeCategory, onCategoryClick }: CategoryRowProps) => {
  const displayCategories = ["All", ...Object.values(MCPCategory)];

  return (
    <div className="flex items-center w-full py-1">
      <div className="flex-grow overflow-x-auto whitespace-nowrap space-x-2 no-scrollbar">
        {displayCategories.map((category) => {
          const isActive = activeCategory === category;
          const isAllButton = category === "All";
          return (
            <Button
              key={category}
              variant="outline"
              className={cn(
                "rounded-lg h-9 align-middle text-sm flex-shrink-0",
                "focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
                isAllButton ? "px-5" : "px-3",
                isActive && isAllButton
                  ? "bg-brand-primary text-brand-white-text hover:bg-brand-primary/90 border-transparent"
                  : isActive
                  ? "bg-brand-primary/10 text-brand-primary border-brand-primary hover:bg-brand-primary/20"
                  : "bg-brand-input text-brand-secondary-font hover:bg-brand-clicked border-brand-stroke dark:bg-brand-card dark:text-brand-secondary-font dark:hover:bg-brand-primary/10 dark:border-brand-stroke"
              )}
              onClick={() => onCategoryClick(category)}
            >
              {sanitizeString(category)}
            </Button>
          );
        })}
      </div>
      <Button
        variant="ghost"
        size="icon"
        className="flex-shrink-0 text-brand-secondary-font hover:text-brand-primary-font dark:text-brand-secondary-font dark:hover:text-brand-white-text ml-1"
      >
        <ChevronRight className="h-5 w-5" />
      </Button>
    </div>
  );
};
